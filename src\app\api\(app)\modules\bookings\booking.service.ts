import { prisma } from '@/db';
import { CreateBooking, UpdateBooking, GetBookingsQuery, CancelBooking, GetStatsQuery } from './bookings.validators';
import { MeetingRoomService } from '../rooms/meeting-room.service';
import { DeskService } from '../desks/desk.service';

export class BookingService {
  static async createBooking(data: CreateBooking, userId: string) {
    const { resourceType, resourceId, title, description, startDate, endDate, attendees } = data;

    const start = new Date(startDate);
    const end = new Date(endDate);

    // Validate dates
    if (start >= end) {
      throw new Error('Start date must be before end date');
    }

    if (start < new Date()) {
      throw new Error('Cannot create bookings in the past');
    }

    // Validate resource exists
    if (resourceType === 'MEETING_ROOM') {
      await MeetingRoomService.getMeetingRoomById(resourceId);

      // Check availability for meeting room
      const availability = await MeetingRoomService.checkAvailability(resourceId, {
        startDate: startDate,
        endDate: endDate
      });

      if (!availability.available) {
        throw new Error(`Meeting room not available during this time. Conflicts: ${availability.conflicts?.map(c => c.title).join(', ')}`);
      }
    } else {
      await DeskService.getDeskById(resourceId);

      // For desk bookings, ensure it's a full day booking
      const dayStart = new Date(start);
      dayStart.setHours(0, 0, 0, 0);
      const dayEnd = new Date(start);
      dayEnd.setHours(23, 59, 59, 999);

      // Check availability for desk
      const availability = await DeskService.checkAvailability(resourceId, {
        date: dayStart.toISOString(),
        userId
      });

      if (!availability.available) {
        throw new Error(`Desk not available on this date. Booked by: ${availability.conflict?.user.name}`);
      }
    }

    try {
      const booking = await prisma.booking.create({
        data: {
          userId,
          resourceType,
          resourceId, // Add the required resourceId field
          title,
          description,
          startDate: start,
          endDate: end,
          attendees: attendees as any || {},
          notifications: {
            slack: { sent: false },
            email: { sent: false }
          },
          ...(resourceType === 'MEETING_ROOM' ? { meetingRoomId: resourceId } : { deskId: resourceId })
        },
        include: {
          user: {
            select: { id: true, name: true, email: true }
          },
          meetingRoom: true,
          desk: true,
          guests: true
        }
      });

      return booking;
    } catch (error: any) {
      throw new Error(`Failed to create booking: ${error.message}`);
    }
  }

  static async getAllBookings(query: GetBookingsQuery = {}) {
    const {
      page = 1,
      limit = 10,
      startDate,
      endDate,
      status,
      resourceType,
      resourceId,
      userId
    } = query;

    const skip = (page - 1) * limit;
    const where: any = {};

    if (startDate || endDate) {
      where.startDate = {};
      if (startDate) where.startDate.gte = new Date(startDate);
      if (endDate) where.startDate.lte = new Date(endDate);
    }

    if (status) {
      where.status = status;
    }

    if (resourceType) {
      where.resourceType = resourceType;
    }

    if (resourceId) {
      if (resourceType === 'MEETING_ROOM') {
        where.meetingRoomId = resourceId;
      } else {
        where.deskId = resourceId;
      }
    }

    if (userId) {
      where.userId = userId;
    }

    const [bookings, total] = await Promise.all([
      prisma.booking.findMany({
        where,
        skip,
        take: limit,
        include: {
          user: {
            select: { id: true, name: true, email: true }
          },
          meetingRoom: true,
          desk: true,
          guests: true
        },
        orderBy: { startDate: 'desc' }
      }),
      prisma.booking.count({ where })
    ]);

    return {
      data: bookings,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  static async getBookingById(id: string, userId?: string) {
    const where: any = { id };

    // If userId is provided, ensure user can only see their own bookings or bookings they're invited to
    if (userId) {
      where.OR = [
        { userId: userId },
        { attendees: { path: ['internal'], array_contains: userId } }
      ];
    }

    const booking = await prisma.booking.findFirst({
      where,
      include: {
        user: {
          select: { id: true, name: true, email: true }
        },
        meetingRoom: true,
        desk: true,
        guests: {
          include: {
            host: {
              select: { id: true, name: true, email: true }
            }
          }
        }
      }
    });

    if (!booking) {
      throw new Error('Booking not found');
    }

    return booking;
  }

  static async updateBooking(id: string, data: UpdateBooking, userId: string) {
    const booking = await this.getBookingById(id, userId);

    // Only the booking owner can update
    if (booking.userId !== userId) {
      throw new Error('You can only update your own bookings');
    }

    // Don't allow updating past bookings
    if (booking.startDate < new Date()) {
      throw new Error('Cannot update past bookings');
    }

    // If changing dates, validate and check availability
    if (data.startDate || data.endDate) {
      const newStartDate = data.startDate ? new Date(data.startDate) : booking.startDate;
      const newEndDate = data.endDate ? new Date(data.endDate) : booking.endDate;

      if (newStartDate >= newEndDate) {
        throw new Error('Start date must be before end date');
      }

      if (newStartDate < new Date()) {
        throw new Error('Cannot set booking dates in the past');
      }

      // Check availability with the new dates
      if (booking.resourceType === 'MEETING_ROOM') {
        const availability = await MeetingRoomService.checkAvailability(booking.meetingRoomId!, {
          startDate: newStartDate.toISOString(),
          endDate: newEndDate.toISOString(),
          excludeBookingId: id
        });

        if (!availability.available) {
          throw new Error(`Meeting room not available during this time. Conflicts: ${availability.conflicts?.map(c => c.title).join(', ')}`);
        }
      } else {
        const availability = await DeskService.checkAvailability(booking.deskId!, {
          date: newStartDate.toISOString(),
          userId
        });

        if (!availability.available && availability.conflict?.bookingId !== id) {
          throw new Error(`Desk not available on this date. Booked by: ${availability.conflict?.user.name}`);
        }
      }
    }

    try {
      const updatedBooking = await prisma.booking.update({
        where: { id },
        data: {
          ...data,
          startDate: data.startDate ? new Date(data.startDate) : undefined,
          endDate: data.endDate ? new Date(data.endDate) : undefined,
          attendees: data.attendees as any
        },
        include: {
          user: {
            select: { id: true, name: true, email: true }
          },
          meetingRoom: true,
          desk: true,
          guests: true
        }
      });

      return updatedBooking;
    } catch (error: any) {
      throw new Error(`Failed to update booking: ${error.message}`);
    }
  }

  static async cancelBooking(id: string, data: CancelBooking, userId: string) {
    const booking = await this.getBookingById(id, userId);

    // Only the booking owner can cancel
    if (booking.userId !== userId) {
      throw new Error('You can only cancel your own bookings');
    }

    // Don't allow canceling past bookings
    if (booking.startDate < new Date()) {
      throw new Error('Cannot cancel past bookings');
    }

    if (booking.status === 'CANCELLED') {
      throw new Error('Booking is already cancelled');
    }

    const updatedBooking = await prisma.booking.update({
      where: { id },
      data: {
        status: 'CANCELLED',
        cancellationReason: data.reason,
        cancelledAt: new Date(),
        cancelledById: userId
      },
      include: {
        user: {
          select: { id: true, name: true, email: true }
        },
        meetingRoom: true,
        desk: true,
        guests: true
      }
    });

    return updatedBooking;
  }

  static async checkInBooking(id: string, userId: string) {
    const booking = await this.getBookingById(id);

    if (booking.status !== 'CONFIRMED') {
      throw new Error('Only confirmed bookings can be checked in');
    }

    const now = new Date();
    const bookingStart = new Date(booking.startDate);
    const timeDiff = (now.getTime() - bookingStart.getTime()) / (1000 * 60); // minutes

    // Allow check-in 15 minutes before and 30 minutes after start time
    if (timeDiff < -15 || timeDiff > 30) {
      throw new Error('Check-in is only allowed 15 minutes before to 30 minutes after the booking start time');
    }

    const updatedBooking = await prisma.booking.update({
      where: { id },
      data: {
        checkIn: { time: now, by: userId },
        checkInUserId: userId
      },
      include: {
        user: {
          select: { id: true, name: true, email: true }
        },
        meetingRoom: true,
        desk: true
      }
    });

    return updatedBooking;
  }

  static async checkOutBooking(id: string, userId: string) {
    const booking = await this.getBookingById(id);

    if (!booking.checkIn) {
      throw new Error('Booking must be checked in before checking out');
    }

    const updatedBooking = await prisma.booking.update({
      where: { id },
      data: {
        checkOut: { time: new Date(), by: userId },
        checkOutUserId: userId,
        status: 'COMPLETED'
      },
      include: {
        user: {
          select: { id: true, name: true, email: true }
        },
        meetingRoom: true,
        desk: true
      }
    });

    return updatedBooking;
  }

  static async getBookingStats(query: GetStatsQuery = {}) {
    const { startDate, endDate, userId } = query;

    const where: any = {};

    if (startDate || endDate) {
      where.startDate = {};
      if (startDate) where.startDate.gte = new Date(startDate);
      if (endDate) where.startDate.lte = new Date(endDate);
    }

    if (userId) {
      where.userId = userId;
    }

    const [
      totalBookings,
      confirmedBookings,
      cancelledBookings,
      completedBookings,
      meetingRoomBookings,
      deskBookings
    ] = await Promise.all([
      prisma.booking.count({ where }),
      prisma.booking.count({ where: { ...where, status: 'CONFIRMED' } }),
      prisma.booking.count({ where: { ...where, status: 'CANCELLED' } }),
      prisma.booking.count({ where: { ...where, status: 'COMPLETED' } }),
      prisma.booking.count({ where: { ...where, resourceType: 'MEETING_ROOM' } }),
      prisma.booking.count({ where: { ...where, resourceType: 'DESK' } })
    ]);

    return {
      totalBookings,
      confirmedBookings,
      cancelledBookings,
      completedBookings,
      meetingRoomBookings,
      deskBookings
    };
  }

  static async getTodayBookings() {
    const today = new Date();
    const startOfDay = new Date(today);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(today);
    endOfDay.setHours(23, 59, 59, 999);

    const bookings = await prisma.booking.findMany({
      where: {
        startDate: { gte: startOfDay, lte: endOfDay },
        status: 'CONFIRMED'
      },
      include: {
        user: {
          select: { id: true, name: true, email: true }
        },
        meetingRoom: true,
        desk: true
      },
      orderBy: { startDate: 'asc' }
    });

    return bookings;
  }

  static async getUserBookings(userId: string, query: any = {}) {
    const {
      page = 1,
      limit = 10,
      startDate,
      endDate,
      status,
      resourceType
    } = query;

    const skip = (page - 1) * limit;
    const where: any = {
      OR: [
        { userId: userId },
        { attendees: { path: ['internal'], array_contains: userId } }
      ]
    };

    if (startDate || endDate) {
      where.startDate = {};
      if (startDate) where.startDate.gte = new Date(startDate);
      if (endDate) where.startDate.lte = new Date(endDate);
    }

    if (status) {
      where.status = status;
    }

    if (resourceType) {
      where.resourceType = resourceType;
    }

    const [bookings, total] = await Promise.all([
      prisma.booking.findMany({
        where,
        skip,
        take: limit,
        include: {
          user: {
            select: { id: true, name: true, email: true }
          },
          meetingRoom: true,
          desk: true,
          guests: true
        },
        orderBy: { startDate: 'desc' }
      }),
      prisma.booking.count({ where })
    ]);

    return {
      data: bookings,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }
}
