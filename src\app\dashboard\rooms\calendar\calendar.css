/* Custom styles for React Big Calendar */

.rbc-calendar {
  font-family: inherit;
}

.rbc-header {
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  padding: 12px 8px;
  font-weight: 600;
  color: #374151;
  text-align: center;
}

.rbc-today {
  background-color: #fef3c7;
}

.rbc-off-range-bg {
  background-color: #f9fafb;
}

.rbc-event {
  border-radius: 6px;
  border: none;
  padding: 2px 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  z-index: 10;
  /* Higher z-index to be above time slots */
  margin: 1px;
  /* Small margin to separate overlapping events */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.rbc-event:hover {
  opacity: 0.9;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rbc-event-label {
  font-size: 11px;
  font-weight: 400;
}

.rbc-slot-selection {
  background-color: rgba(147, 51, 234, 0.2);
  border: 2px solid #9333ea;
  border-radius: 4px;
}

.rbc-time-slot {
  border-top: 1px solid #f1f5f9;
  position: relative !important;
  z-index: 1 !important;
  min-height: 15px !important;
  cursor: pointer !important;
}

/* Hide slot borders when there's an event covering them */
.rbc-event-container .rbc-time-slot {
  border-top: none !important;
}

/* Remove borders from slots that are covered by events */
.rbc-time-content .rbc-time-slot:has(+ .rbc-event),
.rbc-time-content .rbc-time-slot:has(.rbc-event) {
  border-top: none !important;
}

.rbc-time-slot:hover {
  background-color: #f8fafc;
}

/* Override any conflicting styles */
.rbc-time-view .rbc-time-slot,
.rbc-day-view .rbc-time-slot,
.rbc-week-view .rbc-time-slot {
  cursor: pointer !important;
  position: relative !important;
}

.rbc-timeslot-group {
  border-bottom: 1px solid #e2e8f0;
}

.rbc-time-gutter .rbc-timeslot-group {
  border-bottom: 1px solid #e2e8f0;
}

.rbc-time-header-gutter {
  background-color: #f8fafc;
}

.rbc-time-header-content {
  border-left: 1px solid #e2e8f0;
}

.rbc-time-content {
  border-top: 1px solid #e2e8f0;
}

.rbc-time-view .rbc-time-gutter {
  background-color: #f8fafc;
  border-right: 1px solid #e2e8f0;
}

.rbc-time-view .rbc-time-gutter .rbc-time-slot {
  color: #6b7280;
  font-size: 12px;
  text-align: right;
  padding-right: 8px;
}

.rbc-day-slot .rbc-time-slot {
  border-top: 1px solid #f1f5f9;
}

.rbc-current-time-indicator {
  background-color: #ef4444;
  height: 2px;
  z-index: 3;
}

.rbc-toolbar {
  margin-bottom: 20px;
  padding: 0 16px;
}

.rbc-toolbar button {
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 8px 16px;
  margin: 0 2px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.rbc-toolbar button:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.rbc-toolbar button.rbc-active {
  background-color: #9333ea;
  border-color: #9333ea;
  color: white;
}

.rbc-toolbar button:focus {
  outline: 2px solid #9333ea;
  outline-offset: 2px;
}

.rbc-toolbar .rbc-toolbar-label {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0 16px;
  flex-grow: 1;
  text-align: center;
}

.rbc-btn-group {
  display: flex;
  gap: 4px;
}

.rbc-btn-group button:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.rbc-btn-group button:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.rbc-btn-group button:not(:first-child):not(:last-child) {
  border-radius: 0;
}

/* Week view specific styles */
.rbc-time-view .rbc-header {
  border-bottom: 1px solid #e2e8f0;
}

.rbc-time-view .rbc-allday-cell {
  background-color: #f8fafc;
}

/* Month view specific styles */
.rbc-month-view {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.rbc-month-row {
  border-bottom: 1px solid #e2e8f0;
}

.rbc-month-row:last-child {
  border-bottom: none;
}

.rbc-date-cell {
  border-right: 1px solid #e2e8f0;
  padding: 8px;
  min-height: 100px;
}

.rbc-date-cell:last-child {
  border-right: none;
}

.rbc-date-cell.rbc-off-range {
  background-color: #f9fafb;
  color: #9ca3af;
}

.rbc-date-cell.rbc-today {
  background-color: #fef3c7;
}

.rbc-date-cell>a {
  color: #374151;
  font-weight: 600;
  text-decoration: none;
}

.rbc-date-cell.rbc-today>a {
  color: #92400e;
}

.rbc-show-more {
  background-color: #e5e7eb;
  color: #374151;
  border: none;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 11px;
  cursor: pointer;
  margin-top: 2px;
}

/* More specific selectors for better hover coverage - only when no events present */
.rbc-time-view .rbc-time-slot:hover:not(:has(.rbc-event)),
.rbc-day-slot .rbc-time-slot:hover:not(:has(.rbc-event)),
.rbc-time-content .rbc-time-slot:hover:not(:has(.rbc-event)) {
  background-color: #f3f4f6 !important;
  border-left: 3px solid #9333ea !important;
  border-right: 3px solid #9333ea !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

/* Disable hover effects when events are present */
.rbc-time-slot:has(.rbc-event):hover {
  background-color: transparent !important;
  border: none !important;
  cursor: default !important;
}

/* Ensure the entire slot area is hoverable */
.rbc-time-slot {
  position: relative !important;
  z-index: 1 !important;
}

/* Make sure child elements don't block hover */
.rbc-time-slot * {
  pointer-events: none !important;
}

/* Specific styles for week view time slots - only when no events */
.rbc-time-view .rbc-time-content .rbc-time-slot:not(:has(.rbc-event)) {
  cursor: pointer !important;
  width: 100% !important;
  height: 100% !important;
  display: block !important;
}

.rbc-time-view .rbc-time-content .rbc-time-slot:not(:has(.rbc-event)):hover {
  background-color: #ede9fe !important;
  border-left: 3px solid #9333ea !important;
  border-right: 3px solid #9333ea !important;
  border-top: 1px solid #9333ea !important;
  border-bottom: 1px solid #9333ea !important;
}

/* Ensure the time gutter doesn't interfere */
.rbc-time-gutter .rbc-time-slot:hover {
  background-color: transparent !important;
  border: none !important;
}

/* Add pseudo-element to cover entire slot area - only for empty slots */
.rbc-time-content .rbc-time-slot:not(:has(.rbc-event))::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: auto;
}

.rbc-time-content .rbc-time-slot:not(:has(.rbc-event)):hover::before {
  background-color: rgba(147, 51, 234, 0.1);
}

/* Make the entire slot area clickable */
.rbc-time-slot {
  display: block !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

.rbc-show-more:hover {
  background-color: #d1d5db;
}

/* Custom event colors for different statuses */
.rbc-event.event-confirmed {
  background-color: #3b82f6;
  color: white;
}

.rbc-event.event-completed {
  background-color: #10b981;
  color: white;
}

.rbc-event.event-cancelled {
  background-color: #ef4444;
  color: white;
}

.rbc-event.event-no-show {
  background-color: #6b7280;
  color: white;
}

/* Improved overlapping events styling */
.rbc-event-container {
  position: relative;
}

/* Multiple events in same time slot - create visual separation */
.rbc-event+.rbc-event {
  margin-left: 2px !important;
  border-left: 2px solid white !important;
}

/* Different colors for overlapping events */
.rbc-event:nth-child(1) {
  background-color: #3b82f6 !important;
  /* Blue */
}

.rbc-event:nth-child(2) {
  background-color: #10b981 !important;
  /* Green */
}

.rbc-event:nth-child(3) {
  background-color: #f59e0b !important;
  /* Amber */
}

.rbc-event:nth-child(4) {
  background-color: #ef4444 !important;
  /* Red */
}

.rbc-event:nth-child(5) {
  background-color: #8b5cf6 !important;
  /* Purple */
}

.rbc-event:nth-child(6) {
  background-color: #06b6d4 !important;
  /* Cyan */
}

/* Add small colored dots for better distinction */
.rbc-event::before {
  content: '';
  position: absolute;
  left: 2px;
  top: 2px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 1;
}

/* Ensure text is readable */
.rbc-event {
  color: white !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  font-weight: 600;
}

/* Better spacing for event content */
.rbc-event-content {
  padding-left: 12px;
  /* Make room for the dot */
  line-height: 1.2;
}

/* Remove slot borders that are covered by events */
.rbc-time-slot:has(.rbc-event) {
  border-top: none !important;
}

/* Ensure events that span multiple slots look seamless */
.rbc-event {
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  backdrop-filter: blur(1px);
}

/* Special styling for events that span multiple time slots */
.rbc-event[style*="height"] {
  border-top: 2px solid rgba(255, 255, 255, 0.3) !important;
  border-bottom: 2px solid rgba(255, 255, 255, 0.3) !important;
}

/* Hide time slot borders when events are present */
.rbc-day-slot:has(.rbc-event) .rbc-time-slot {
  border-top: transparent !important;
}

/* Improve visual hierarchy for overlapping events */
.rbc-event-container .rbc-event:first-child {
  z-index: 15;
}

.rbc-event-container .rbc-event:nth-child(2) {
  z-index: 14;
  margin-left: 4px !important;
}

.rbc-event-container .rbc-event:nth-child(3) {
  z-index: 13;
  margin-left: 8px !important;
}

.rbc-event-container .rbc-event:nth-child(4) {
  z-index: 12;
  margin-left: 12px !important;
}

/* Fallback for browsers that don't support :has() */
@supports not (selector(:has(*))) {

  /* Alternative approach for hiding hover on slots with events */
  .rbc-time-slot {
    position: relative;
  }

  .rbc-event {
    pointer-events: auto;
  }

  /* Reduce hover effects when events might be present */
  .rbc-time-view .rbc-time-slot:hover {
    background-color: rgba(243, 244, 246, 0.5) !important;
    border-left: 2px solid rgba(147, 51, 234, 0.5) !important;
    border-right: 2px solid rgba(147, 51, 234, 0.5) !important;
  }
}

/* Additional styles for better event separation */
.rbc-event-container {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

/* Ensure events don't overlap time slot borders */
.rbc-time-content .rbc-event {
  margin-top: -1px;
  margin-bottom: -1px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .rbc-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .rbc-toolbar .rbc-toolbar-label {
    order: -1;
    margin: 0;
    text-align: center;
  }

  .rbc-btn-group {
    justify-content: center;
  }

  .rbc-time-view .rbc-time-gutter .rbc-time-slot {
    font-size: 10px;
    padding-right: 4px;
  }
}