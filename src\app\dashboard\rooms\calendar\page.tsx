'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  ArrowLeft,
  Building,
  Users,
  MapPin,
  Calendar,
  Clock,
  Plus,
  X,
  CheckCircle,
  XCircle,
  AlertCircle,
  Trash2
} from 'lucide-react';
import Link from 'next/link';
import { Calendar as BigCalendar, luxonLocalizer } from 'react-big-calendar';
import { DateTime, Settings } from 'luxon';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import './calendar.css';
import { useMeetingRooms } from '@/lib/hooks/use-meeting-rooms';
import { useBookings, useCreateBooking, useCancelBooking, CreateBookingInput } from '@/lib/hooks/use-bookings';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

// Configure Luxon for Spanish locale
Settings.defaultLocale = 'es';
const localizer = luxonLocalizer(DateTime);

interface BookingEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  resource: any;
  booking: any;
}

interface CreateBookingFormData {
  title: string;
  description?: string;
  resourceId: string;
  startTime: string;
  endTime: string;
  attendees?: {
    internal?: string[];
    external?: string[];
  };
}

export default function MeetingRoomsCalendarPage() {
  // Modal states
  const [showEventModal, setShowEventModal] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [view] = useState<'month' | 'week' | 'day'>('week');
  const [selectedEvent, setSelectedEvent] = useState<BookingEvent | null>(null);
  const [selectedSlot, setSelectedSlot] = useState<{ start: Date; end: Date; resourceId?: string } | null>(null);
  const [selectedRoom, setSelectedRoom] = useState<any>(null);
  const [validationError, setValidationError] = useState<string>('');

  // Attendees state for create form
  const [internalAttendees, setInternalAttendees] = useState<string[]>([]);
  const [externalAttendees, setExternalAttendees] = useState<string[]>([]);
  const [newInternalAttendee, setNewInternalAttendee] = useState('');
  const [newExternalAttendee, setNewExternalAttendee] = useState('');

  const { data: meetingRooms } = useMeetingRooms({
    limit: 100,
    isActive: true
  });

  // Fetch all bookings for calendar
  const { data: bookingsData } = useBookings({
    limit: 1000,
    resourceType: 'MEETING_ROOM'
  });

  const createBooking = useCreateBooking();
  const cancelBooking = useCancelBooking();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue,
    watch
  } = useForm<CreateBookingFormData>();

  // Transform bookings to calendar events
  const events: BookingEvent[] = bookingsData?.data?.map((booking: any) => ({
    id: booking.id,
    title: booking.title,
    start: new Date(booking.startDate),
    end: new Date(booking.endDate),
    resource: booking.meetingRoom,
    booking: booking
  })) || [];

  const formatDateTime = (date: Date | string) => {
    return DateTime.fromJSDate(new Date(date)).toFormat('dd/MM/yyyy HH:mm');
  };

  const formatTime = (date: Date | string) => {
    return DateTime.fromJSDate(new Date(date)).toFormat('HH:mm');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'CONFIRMED':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'COMPLETED':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'NO_SHOW':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'CONFIRMED':
        return <CheckCircle className="h-4 w-4" />;
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4" />;
      case 'CANCELLED':
        return <XCircle className="h-4 w-4" />;
      case 'NO_SHOW':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const handleSelectEvent = (event: BookingEvent) => {
    setSelectedEvent(event);
    setShowEventModal(true);
  };

  const handleSelectSlot = ({ start, end }: { start: Date; end: Date }) => {
    setSelectedSlot({ start, end });
    setShowCreateModal(true);
    reset();
    setInternalAttendees([]);
    setExternalAttendees([]);
    setSelectedRoom(null);
    setValidationError('');

    // Set initial times
    setValue('startTime', DateTime.fromJSDate(start).toFormat('HH:mm'));
    setValue('endTime', DateTime.fromJSDate(end).toFormat('HH:mm'));
  };

  const addInternalAttendee = () => {
    if (newInternalAttendee.trim() && !internalAttendees.includes(newInternalAttendee.trim())) {
      const updated = [...internalAttendees, newInternalAttendee.trim()];
      setInternalAttendees(updated);
      setNewInternalAttendee('');
    }
  };

  const addExternalAttendee = () => {
    if (newExternalAttendee.trim() && !externalAttendees.includes(newExternalAttendee.trim())) {
      const updated = [...externalAttendees, newExternalAttendee.trim()];
      setExternalAttendees(updated);
      setNewExternalAttendee('');
    }
  };

  const removeInternalAttendee = (attendee: string) => {
    setInternalAttendees(prev => prev.filter(a => a !== attendee));
  };

  const removeExternalAttendee = (attendee: string) => {
    setExternalAttendees(prev => prev.filter(a => a !== attendee));
  };

  const handleRoomSelection = (roomId: string) => {
    const room = meetingRooms?.data?.find(r => r.id === roomId);
    setSelectedRoom(room);
    setValue('resourceId', roomId);

    // Adjust end time based on minimum duration
    if (room?.bookingRules && typeof room.bookingRules === 'object' && selectedSlot) {
      const rules = room.bookingRules as any;
      if (rules.minBookingDuration) {
        const startTime = DateTime.fromJSDate(selectedSlot.start);
        const minEndTime = startTime.plus({ minutes: rules.minBookingDuration });
        setValue('endTime', minEndTime.toFormat('HH:mm'));
      }
    }
  };

  const validateBookingRules = useCallback((startTime: string, endTime: string, bookingDate?: Date) => {
    if (!selectedRoom?.bookingRules || typeof selectedRoom.bookingRules !== 'object') {
      return { isValid: true, error: '' };
    }

    const rules = selectedRoom.bookingRules as any;
    const start = DateTime.fromFormat(startTime, 'HH:mm');
    const end = DateTime.fromFormat(endTime, 'HH:mm');
    const durationMinutes = end.diff(start, 'minutes').minutes;

    // Validate minimum duration
    if (rules.minBookingDuration && durationMinutes < rules.minBookingDuration) {
      return {
        isValid: false,
        error: `La duración mínima es ${rules.minBookingDuration} minutos`
      };
    }

    // Validate maximum duration
    if (rules.maxBookingDuration && durationMinutes > rules.maxBookingDuration) {
      return {
        isValid: false,
        error: `La duración máxima es ${Math.floor(rules.maxBookingDuration / 60)} horas`
      };
    }

    // Validate advance booking days
    if (rules.advanceBookingDays && bookingDate) {
      const now = DateTime.now();
      const bookingDateTime = DateTime.fromJSDate(bookingDate);
      const daysDifference = bookingDateTime.diff(now, 'days').days;

      if (daysDifference > rules.advanceBookingDays) {
        return {
          isValid: false,
          error: `No se puede reservar con más de ${rules.advanceBookingDays} días de anticipación`
        };
      }
    }

    // Validate minimum notice hours
    if (rules.minNoticeHours && bookingDate) {
      const now = DateTime.now();
      const bookingDateTime = DateTime.fromJSDate(bookingDate).set({
        hour: parseInt(startTime.split(':')[0]),
        minute: parseInt(startTime.split(':')[1])
      });
      const hoursDifference = bookingDateTime.diff(now, 'hours').hours;

      if (hoursDifference < rules.minNoticeHours) {
        return {
          isValid: false,
          error: `Se requiere un aviso mínimo de ${rules.minNoticeHours} horas`
        };
      }
    }

    // Validate business hours
    if (rules.businessHours) {
      const businessStart = DateTime.fromFormat(rules.businessHours.start || '09:00', 'HH:mm');
      const businessEnd = DateTime.fromFormat(rules.businessHours.end || '18:00', 'HH:mm');

      if (start < businessStart || end > businessEnd) {
        return {
          isValid: false,
          error: `Las reservas deben estar dentro del horario laboral (${rules.businessHours.start || '09:00'} - ${rules.businessHours.end || '18:00'})`
        };
      }
    }

    return { isValid: true, error: '' };
  }, [selectedRoom]);



  const validateCapacity = useCallback(() => {
    if (!selectedRoom?.capacity) return { isValid: true, error: '' };

    const totalAttendees = internalAttendees.length + externalAttendees.length + 1; // +1 for organizer

    if (totalAttendees > selectedRoom.capacity) {
      return {
        isValid: false,
        error: `La sala tiene capacidad para ${selectedRoom.capacity} personas. Tienes ${totalAttendees} asistentes.`
      };
    }

    return { isValid: true, error: '' };
  }, [selectedRoom, internalAttendees, externalAttendees]);

  const onSubmitCreate = async (data: CreateBookingFormData) => {
    try {
      if (!selectedSlot) {
        throw new Error('No se ha seleccionado un horario');
      }

      // Validate booking rules
      const rulesValidation = validateBookingRules(data.startTime, data.endTime, selectedSlot.start);
      if (!rulesValidation.isValid) {
        toast.error(rulesValidation.error);
        return;
      }

      // Validate capacity
      const capacityValidation = validateCapacity();
      if (!capacityValidation.isValid) {
        toast.error(capacityValidation.error);
        return;
      }

      // Create start and end dates with the selected times
      const selectedDate = DateTime.fromJSDate(selectedSlot.start);
      const startDateTime = selectedDate.set({
        hour: parseInt(data.startTime.split(':')[0]),
        minute: parseInt(data.startTime.split(':')[1])
      });
      const endDateTime = selectedDate.set({
        hour: parseInt(data.endTime.split(':')[0]),
        minute: parseInt(data.endTime.split(':')[1])
      });

      const bookingData: CreateBookingInput = {
        resourceType: 'MEETING_ROOM',
        resourceId: data.resourceId,
        title: data.title,
        description: data.description,
        startDate: startDateTime.toISO() || '',
        endDate: endDateTime.toISO() || '',
        attendees: {
          internal: internalAttendees,
          external: externalAttendees
        }
      };

      await createBooking.mutateAsync(bookingData);
      setShowCreateModal(false);
      toast.success('Reserva creada exitosamente');
    } catch (error: any) {
      toast.error(error.message || 'Error al crear la reserva');
    }
  };

  // Watch for changes in form values to validate in real-time
  const startTime = watch('startTime');
  const endTime = watch('endTime');

  useEffect(() => {
    // Real-time validation when times change
    if (!startTime || !endTime || !selectedRoom || !selectedSlot) {
      setValidationError('');
      return;
    }

    const rulesValidation = validateBookingRules(startTime, endTime, selectedSlot.start);
    const capacityValidation = validateCapacity();

    if (!rulesValidation.isValid) {
      setValidationError(rulesValidation.error);
    } else if (!capacityValidation.isValid) {
      setValidationError(capacityValidation.error);
    } else {
      setValidationError('');
    }
  }, [startTime, endTime, selectedRoom, internalAttendees, externalAttendees, selectedSlot, validateBookingRules, validateCapacity]);

  const handleCancelBooking = async () => {
    try {
      if (!selectedEvent) return;

      await cancelBooking.mutateAsync({
        id: selectedEvent.id,
        reason: 'Cancelado por el usuario'
      });
      setShowCancelDialog(false);
      setShowEventModal(false);
      toast.success('Reserva cancelada exitosamente');
    } catch (error: any) {
      toast.error(error.message || 'Error al cancelar la reserva');
    }
  };

  const canCancelBooking = (booking: any) => {
    if (!booking) return false;
    const now = new Date();
    const bookingStart = new Date(booking.startDate);
    return booking.status === 'CONFIRMED' && bookingStart > now;
  };

  const eventStyleGetter = (event: BookingEvent) => {
    let className = 'event-confirmed';
    let backgroundColor = '#3b82f6'; // Default blue

    // Color based on status
    switch (event.booking.status) {
      case 'CONFIRMED':
        className = 'event-confirmed';
        backgroundColor = '#3b82f6'; // Blue
        break;
      case 'COMPLETED':
        className = 'event-completed';
        backgroundColor = '#10b981'; // Green
        break;
      case 'CANCELLED':
        className = 'event-cancelled';
        backgroundColor = '#ef4444'; // Red
        break;
      case 'NO_SHOW':
        className = 'event-no-show';
        backgroundColor = '#6b7280'; // Gray
        break;
    }

    // Find overlapping events to assign different colors
    const overlappingEvents = events.filter(e =>
      e.id !== event.id &&
      ((e.start <= event.start && e.end > event.start) ||
        (e.start < event.end && e.end >= event.end) ||
        (e.start >= event.start && e.end <= event.end))
    );

    // Assign different colors for overlapping events
    const eventColors = [
      '#3b82f6', // Blue
      '#10b981', // Green
      '#f59e0b', // Amber
      '#ef4444', // Red
      '#8b5cf6', // Purple
      '#06b6d4', // Cyan
      '#f97316', // Orange
      '#84cc16'  // Lime
    ];

    if (overlappingEvents.length > 0) {
      const eventIndex = events.findIndex(e => e.id === event.id);
      backgroundColor = eventColors[eventIndex % eventColors.length];
    }

    return {
      className,
      style: {
        backgroundColor,
        borderRadius: '6px',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        display: 'block',
        color: 'white',
        fontWeight: '600',
        textShadow: '0 1px 2px rgba(0, 0, 0, 0.3)',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        zIndex: 10,
        position: 'relative' as const
      }
    };
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="icon" asChild className="rounded-full border-gray-300 text-gray-700 hover:bg-gray-50">
              <Link href="/dashboard/rooms">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Calendario de Salas</h1>
              <p className="text-gray-600">
                Gestiona las reservas de salas de reuniones
              </p>
            </div>
          </div>
        </div>

        {/* Calendar */}
        <Card className="border-0 shadow-lg rounded-2xl bg-white/80 backdrop-blur-sm">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-purple-900">
              <Calendar className="h-5 w-5" />
              Calendario de Reservas
            </CardTitle>
            <CardDescription>
              Haz clic en un evento para ver detalles o en un espacio vacío para crear una nueva reserva
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[600px] rounded-xl overflow-hidden border border-gray-200">
              <BigCalendar
                localizer={localizer}
                events={events}
                startAccessor="start"
                endAccessor="end"
                style={{ height: '100%' }}
                selectable
                onSelectEvent={handleSelectEvent}
                onSelectSlot={handleSelectSlot}
                eventPropGetter={eventStyleGetter}
                views={['month', 'week', 'day']}
                // defaultView="week"
                defaultView={view}
                step={15}
                timeslots={4}
                min={new Date(2024, 0, 1, 8, 0)}
                max={new Date(2024, 0, 1, 20, 0)}
                formats={{
                  timeGutterFormat: 'HH:mm',
                  eventTimeRangeFormat: ({ start, end }) =>
                    `${DateTime.fromJSDate(start).toFormat('HH:mm')} - ${DateTime.fromJSDate(end).toFormat('HH:mm')}`
                }}
                messages={{
                  next: 'Siguiente',
                  previous: 'Anterior',
                  today: 'Hoy',
                  month: 'Mes',
                  week: 'Semana',
                  day: 'Día',
                  agenda: 'Agenda',
                  date: 'Fecha',
                  time: 'Hora',
                  event: 'Evento',
                  noEventsInRange: 'No hay eventos en este rango',
                  showMore: (total) => `+ Ver ${total} más`
                }}
              />
            </div>
          </CardContent>
        </Card>

        {/* Event Details Modal */}
        <Dialog open={showEventModal} onOpenChange={setShowEventModal}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Detalles de la Reserva
              </DialogTitle>
              <DialogDescription>
                Información completa de la reserva seleccionada
              </DialogDescription>
            </DialogHeader>

            {selectedEvent && (
              <div className="space-y-6">
                <div className="grid gap-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">{selectedEvent.title}</h3>
                    <Badge className={`${getStatusColor(selectedEvent.booking.status)} rounded-full px-3 py-1 text-sm font-medium border`}>
                      <span className="flex items-center gap-1">
                        {getStatusIcon(selectedEvent.booking.status)}
                        {selectedEvent.booking.status}
                      </span>
                    </Badge>
                  </div>

                  <div className="grid gap-3 text-sm">
                    <div className="flex items-center gap-3">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <div>
                        <p className="text-gray-500">Fecha y Hora</p>
                        <p className="font-medium">
                          {formatDateTime(selectedEvent.start)} - {formatTime(selectedEvent.end)}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3 ">
                      <Building className="h-4 w-4 text-gray-500" />
                      <div>
                        <p className="text-gray-500 text-start">Sala de Reunión</p>
                        <p className="font-medium">{selectedEvent.resource?.name || 'Sala de Reunión'}</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <MapPin className="h-4 w-4 text-gray-500" />
                      <div>
                        <p className="text-gray-500">Ubicación</p>
                        <p className="font-medium">
                          {selectedEvent.resource?.location?.floor ? `Piso ${selectedEvent.resource.location.floor}` : ''}
                          {selectedEvent.resource?.location?.room ? ` - ${selectedEvent.resource.location.room}` : ''}
                        </p>
                      </div>
                    </div>

                    {selectedEvent.resource?.capacity && (
                      <div className="flex items-center gap-3">
                        <Users className="h-4 w-4 text-gray-500" />
                        <div>
                          <p className="text-gray-500">Capacidad</p>
                          <p className="font-medium">{selectedEvent.resource.capacity} personas</p>
                        </div>
                      </div>
                    )}

                    {selectedEvent.booking.description && (
                      <div>
                        <p className="text-gray-500 mb-1">Descripción</p>
                        <p className="text-gray-700">{selectedEvent.booking.description}</p>
                      </div>
                    )}
                  </div>

                  {/* Attendees */}
                  {(selectedEvent.booking.attendees?.internal?.length > 0 || selectedEvent.booking.attendees?.external?.length > 0) && (
                    <div className="space-y-3">
                      <h4 className="font-medium text-gray-900">Asistentes</h4>

                      {selectedEvent.booking.attendees?.internal && selectedEvent.booking.attendees.internal.length > 0 && (
                        <div>
                          <p className="text-sm text-gray-500 mb-2">Empleados</p>
                          <div className="flex flex-wrap gap-2">
                            {selectedEvent.booking.attendees.internal.map((attendee: string, index: number) => (
                              <Badge key={index} variant="secondary" className="bg-blue-100 text-blue-700">
                                {attendee}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}

                      {selectedEvent.booking.attendees?.external && selectedEvent.booking.attendees.external.length > 0 && (
                        <div>
                          <p className="text-sm text-gray-500 mb-2">Invitados Externos</p>
                          <div className="flex flex-wrap gap-2">
                            {selectedEvent.booking.attendees.external.map((attendee: string, index: number) => (
                              <Badge key={index} variant="secondary" className="bg-green-100 text-green-700">
                                {attendee}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="flex gap-3 pt-4 border-t">
                  <Button variant="outline" className="flex-1" onClick={() => setShowEventModal(false)}>
                    Cerrar
                  </Button>
                  {canCancelBooking(selectedEvent.booking) && (
                    <Button
                      variant="outline"
                      className="border-red-300 text-red-700 hover:bg-red-50"
                      onClick={() => setShowCancelDialog(true)}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Cancelar Reserva
                    </Button>
                  )}
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Create Booking Modal */}
        <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Plus className="h-5 w-5" />
                Nueva Reserva
              </DialogTitle>
              <DialogDescription>
                Crear una nueva reserva para el horario seleccionado
              </DialogDescription>
            </DialogHeader>

            {selectedSlot && (
              <form onSubmit={handleSubmit(onSubmitCreate)} className="space-y-6">
                {/* Selected Date Display */}
                <div className="p-4 bg-purple-50 rounded-xl border border-purple-200">
                  <div className="flex items-center gap-2 text-purple-800">
                    <CheckCircle className="h-5 w-5" />
                    <div>
                      <p className="font-medium">Fecha Seleccionada</p>
                      <p className="text-sm">
                        {DateTime.fromJSDate(selectedSlot.start).toFormat('dd/MM/yyyy')}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Basic Information */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="resourceId" className="text-gray-700 font-medium">Sala de Reunión *</Label>
                    <Select onValueChange={handleRoomSelection}>
                      <SelectTrigger className="rounded-xl border-gray-200 focus:border-purple-500">
                        <SelectValue placeholder="Selecciona una sala" />
                      </SelectTrigger>
                      <SelectContent>
                        {meetingRooms?.data?.map((room: any) => (
                          <SelectItem key={room.id} value={room.id}>
                            <div className="flex flex-col text-start">
                              <span className="font-medium">{room.name}</span>
                              <span className="text-xs text-gray-500">
                                {room.location?.floor ? `Piso ${room.location.floor}` : ''}
                                {room.location?.room ? ` - ${room.location.room}` : ''}
                                {room.capacity ? ` • ${room.capacity} personas` : ''}
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.resourceId && (
                      <p className="text-sm text-red-500">Este campo es requerido</p>
                    )}
                  </div>

                  {/* Room Rules Display */}
                  {selectedRoom && (
                    <div className="p-4 bg-blue-50 rounded-xl border border-blue-200">
                      <h4 className="font-medium text-blue-900 mb-2">Reglas de la Sala</h4>
                      <div className="grid grid-cols-2 gap-3 text-sm text-blue-800">
                        {selectedRoom.bookingRules?.minBookingDuration && (
                          <div>
                            <span className="font-medium">Duración mínima:</span>
                            <span className="ml-1">{selectedRoom.bookingRules.minBookingDuration} min</span>
                          </div>
                        )}
                        {selectedRoom.bookingRules?.maxBookingDuration && (
                          <div>
                            <span className="font-medium">Duración máxima:</span>
                            <span className="ml-1">{Math.floor(selectedRoom.bookingRules.maxBookingDuration / 60)}h</span>
                          </div>
                        )}
                        {selectedRoom.capacity && (
                          <div>
                            <span className="font-medium">Capacidad:</span>
                            <span className="ml-1">{selectedRoom.capacity} personas</span>
                          </div>
                        )}
                        {selectedRoom.bookingRules?.advanceBookingDays && (
                          <div>
                            <span className="font-medium">Reserva anticipada:</span>
                            <span className="ml-1">{selectedRoom.bookingRules.advanceBookingDays} días</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Validation Error Display */}
                  {validationError && (
                    <div className="p-4 bg-red-50 rounded-xl border border-red-200">
                      <p className="text-red-800 text-sm font-medium">{validationError}</p>
                    </div>
                  )}

                  {/* Time Selection */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="startTime" className="text-gray-700 font-medium">Hora de Inicio *</Label>
                      <Input
                        id="startTime"
                        type="time"
                        {...register('startTime', { required: 'La hora de inicio es requerida' })}
                        className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                      />
                      {errors.startTime && (
                        <p className="text-sm text-red-500">{errors.startTime.message}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="endTime" className="text-gray-700 font-medium">Hora de Fin *</Label>
                      <Input
                        id="endTime"
                        type="time"
                        {...register('endTime', { required: 'La hora de fin es requerida' })}
                        className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                      />
                      {errors.endTime && (
                        <p className="text-sm text-red-500">{errors.endTime.message}</p>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="title" className="text-gray-700 font-medium">Título de la Reserva *</Label>
                    <Input
                      id="title"
                      {...register('title', { required: 'El título es requerido' })}
                      placeholder="ej., Reunión de Equipo"
                      className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                    />
                    {errors.title && (
                      <p className="text-sm text-red-500">{errors.title.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description" className="text-gray-700 font-medium">Descripción</Label>
                    <Textarea
                      id="description"
                      {...register('description')}
                      placeholder="Descripción opcional de la reunión..."
                      className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500 min-h-[80px]"
                    />
                  </div>
                </div>

                {/* Attendees */}
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">Asistentes</h4>
                  <Tabs defaultValue="internal" className="space-y-4">
                    <TabsList className="grid w-full grid-cols-2 rounded-xl bg-gray-100">
                      <TabsTrigger value="internal" className="rounded-lg">Internos</TabsTrigger>
                      <TabsTrigger value="external" className="rounded-lg">Externos</TabsTrigger>
                    </TabsList>

                    <TabsContent value="internal" className="space-y-3">
                      <div className="flex gap-2">
                        <Input
                          value={newInternalAttendee}
                          onChange={(e) => setNewInternalAttendee(e.target.value)}
                          placeholder="Email del empleado..."
                          className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                          onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addInternalAttendee())}
                        />
                        <Button
                          type="button"
                          onClick={addInternalAttendee}
                          variant="outline"
                          className="rounded-xl border-purple-200 text-purple-600 hover:bg-purple-50"
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                      {internalAttendees.length > 0 && (
                        <div className="flex flex-wrap gap-2">
                          {internalAttendees.map((attendee) => (
                            <Badge
                              key={attendee}
                              variant="secondary"
                              className="bg-blue-100 text-blue-700 hover:bg-blue-200 rounded-full px-3 py-1"
                            >
                              {attendee}
                              <button
                                type="button"
                                onClick={() => removeInternalAttendee(attendee)}
                                className="ml-2 hover:text-blue-900"
                              >
                                <X className="h-3 w-3" />
                              </button>
                            </Badge>
                          ))}
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent value="external" className="space-y-3">
                      <div className="flex gap-2">
                        <Input
                          value={newExternalAttendee}
                          onChange={(e) => setNewExternalAttendee(e.target.value)}
                          placeholder="Email del invitado externo..."
                          className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                          onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addExternalAttendee())}
                        />
                        <Button
                          type="button"
                          onClick={addExternalAttendee}
                          variant="outline"
                          className="rounded-xl border-purple-200 text-purple-600 hover:bg-purple-50"
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                      {externalAttendees.length > 0 && (
                        <div className="flex flex-wrap gap-2">
                          {externalAttendees.map((attendee) => (
                            <Badge
                              key={attendee}
                              variant="secondary"
                              className="bg-green-100 text-green-700 hover:bg-green-200 rounded-full px-3 py-1"
                            >
                              {attendee}
                              <button
                                type="button"
                                onClick={() => removeExternalAttendee(attendee)}
                                className="ml-2 hover:text-green-900"
                              >
                                <X className="h-3 w-3" />
                              </button>
                            </Badge>
                          ))}
                        </div>
                      )}
                    </TabsContent>
                  </Tabs>
                </div>

                {/* Actions */}
                <div className="flex gap-3 pt-4 border-t">
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1"
                    onClick={() => setShowCreateModal(false)}
                  >
                    Cancelar
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting || !!validationError}
                    className="flex-1 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? 'Creando...' : 'Crear Reserva'}
                  </Button>
                </div>
              </form>
            )}
          </DialogContent>
        </Dialog>

        {/* Cancel Confirmation Dialog */}
        <AlertDialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>¿Cancelar reserva?</AlertDialogTitle>
              <AlertDialogDescription>
                Esta acción no se puede deshacer. La reserva será cancelada y se notificará a todos los asistentes.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Mantener Reserva</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleCancelBooking}
                className="bg-red-600 hover:bg-red-700"
                disabled={cancelBooking.isPending}
              >
                {cancelBooking.isPending ? 'Cancelando...' : 'Sí, Cancelar'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
}
