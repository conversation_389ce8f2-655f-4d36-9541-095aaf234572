'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar, Clock, MapPin, Users, Plus, X } from 'lucide-react';
import { DateTime } from 'luxon';
import { useForm } from 'react-hook-form';
import { useState, useEffect } from 'react';
import { CreateBookingModalProps, CreateBookingFormData } from './types';

export function CreateBookingModal({
  isOpen,
  onClose,
  selectedSlot,
  meetingRooms,
  onSubmit,
  isLoading = false
}: CreateBookingModalProps) {
  const [internalAttendees, setInternalAttendees] = useState<string[]>(['']);
  const [externalAttendees, setExternalAttendees] = useState<string[]>(['']);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    // watch
  } = useForm<CreateBookingFormData>();

  // const selectedRoomId = watch('resourceId');

  useEffect(() => {
    if (isOpen && selectedSlot) {
      const startTime = DateTime.fromJSDate(selectedSlot.start).toFormat('HH:mm');
      const endTime = DateTime.fromJSDate(selectedSlot.end).toFormat('HH:mm');

      setValue('startTime', startTime);
      setValue('endTime', endTime);

      if (selectedSlot.resourceId) {
        setValue('resourceId', selectedSlot.resourceId);
      }
    }
  }, [isOpen, selectedSlot, setValue]);

  const handleClose = () => {
    reset();
    setInternalAttendees(['']);
    setExternalAttendees(['']);
    onClose();
  };

  const onSubmitForm = async (data: CreateBookingFormData) => {
    const formData = {
      ...data,
      internalAttendees: internalAttendees.filter(email => email.trim() !== ''),
      externalAttendees: externalAttendees.filter(email => email.trim() !== '')
    };

    await onSubmit(formData);
    handleClose();
  };

  const addInternalAttendee = () => {
    setInternalAttendees([...internalAttendees, '']);
  };

  const removeInternalAttendee = (index: number) => {
    setInternalAttendees(internalAttendees.filter((_, i) => i !== index));
  };

  const updateInternalAttendee = (index: number, value: string) => {
    const updated = [...internalAttendees];
    updated[index] = value;
    setInternalAttendees(updated);
  };

  const addExternalAttendee = () => {
    setExternalAttendees([...externalAttendees, '']);
  };

  const removeExternalAttendee = (index: number) => {
    setExternalAttendees(externalAttendees.filter((_, i) => i !== index));
  };

  const updateExternalAttendee = (index: number, value: string) => {
    const updated = [...externalAttendees];
    updated[index] = value;
    setExternalAttendees(updated);
  };

  if (!selectedSlot) return null;

  const selectedDate = DateTime.fromJSDate(selectedSlot.start);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-purple-600" />
            Nueva Reserva
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmitForm)} className="space-y-6">
          {/* Date and Time Info */}
          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-purple-600" />
                <span className="font-medium">
                  {selectedDate.toFormat('dd/MM/yyyy')}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-purple-600" />
                <span>
                  {selectedDate.toFormat('HH:mm')} - {DateTime.fromJSDate(selectedSlot.end).toFormat('HH:mm')}
                </span>
              </div>
            </div>
          </div>

          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <Label htmlFor="title">Título de la reunión *</Label>
              <Input
                id="title"
                {...register('title', { required: 'El título es requerido' })}
                placeholder="Ej: Reunión de equipo"
              />
              {errors.title && (
                <p className="text-sm text-red-600 mt-1">{errors.title.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="resourceId">Sala de reuniones *</Label>
              <Select onValueChange={(value) => setValue('resourceId', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Seleccionar sala" />
                </SelectTrigger>
                <SelectContent>
                  {meetingRooms?.map((room) => (
                    <SelectItem key={room.id} value={room.id}>
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4" />
                        {room.name} (Cap: {room.capacity})
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.resourceId && (
                <p className="text-sm text-red-600 mt-1">Selecciona una sala</p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label htmlFor="startTime">Hora inicio</Label>
                <Input
                  id="startTime"
                  type="time"
                  {...register('startTime', { required: true })}
                />
              </div>
              <div>
                <Label htmlFor="endTime">Hora fin</Label>
                <Input
                  id="endTime"
                  type="time"
                  {...register('endTime', { required: true })}
                />
              </div>
            </div>
          </div>

          {/* Description */}
          <div>
            <Label htmlFor="description">Descripción</Label>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="Descripción opcional de la reunión"
              rows={3}
            />
          </div>

          {/* Internal Attendees */}
          <div>
            <Label className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Asistentes Internos
            </Label>
            <div className="space-y-2 mt-2">
              {internalAttendees.map((email, index) => (
                <div key={index} className="flex gap-2">
                  <Input
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => updateInternalAttendee(index, e.target.value)}
                  />
                  {internalAttendees.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => removeInternalAttendee(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addInternalAttendee}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Agregar asistente interno
              </Button>
            </div>
          </div>

          {/* External Attendees */}
          <div>
            <Label className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Invitados Externos
            </Label>
            <div className="space-y-2 mt-2">
              {externalAttendees.map((email, index) => (
                <div key={index} className="flex gap-2">
                  <Input
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => updateExternalAttendee(index, e.target.value)}
                  />
                  {externalAttendees.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => removeExternalAttendee(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addExternalAttendee}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Agregar invitado externo
              </Button>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancelar
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Creando...' : 'Crear Reserva'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
