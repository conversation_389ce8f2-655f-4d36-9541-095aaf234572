// Check if there are users in the database
const checkUsers = async () => {
  try {
    console.log('Checking users in database...');
    
    const response = await fetch('http://localhost:4002/api/users', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    if (response.ok) {
      const result = await response.json();
      console.log('Users response:', JSON.stringify(result, null, 2));
    } else {
      console.log('Users endpoint not available or error:', response.status);
    }
  } catch (error) {
    console.error('Error checking users:', error.message);
  }
};

// Run the check
checkUsers();
