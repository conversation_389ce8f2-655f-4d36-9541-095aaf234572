import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import server from '@/app/api/server';
import { toast } from 'sonner';

// Types
export interface Booking {
  id: string;
  userId: string;
  resourceType: 'MEETING_ROOM' | 'DESK';
  resourceId: string;
  title: string;
  description?: string;
  startDate: Date;
  endDate: Date;
  status: 'CONFIRMED' | 'CANCELLED' | 'COMPLETED' | 'NO_SHOW';
  attendees?: {
    internal?: string[];
    external?: string[];
  };
  notifications?: any;
  checkIn?: any;
  checkOut?: any;
  cancellationReason?: string;
  cancelledAt?: Date;
  cancelledById?: string;
  meetingRoomId?: string;
  deskId?: string;
  user?: any;
  meetingRoom?: any;
  desk?: any;
  guests?: any[];
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateBookingInput {
  resourceType: 'MEETING_ROOM' | 'DESK';
  resourceId: string;
  title: string;
  description?: string;
  startDate: string;
  endDate: string;
  attendees?: {
    internal?: string[];
    external?: string[];
  };
}

export interface BookingFilters {
  page?: number;
  limit?: number;
  startDate?: string;
  endDate?: string;
  status?: 'CONFIRMED' | 'CANCELLED' | 'COMPLETED' | 'NO_SHOW';
  resourceType?: 'MEETING_ROOM' | 'DESK';
  resourceId?: string;
}

// Query Keys
export const bookingKeys = {
  all: ['bookings'] as const,
  lists: () => [...bookingKeys.all, 'list'] as const,
  list: (filters: BookingFilters) => [...bookingKeys.lists(), filters] as const,
  details: () => [...bookingKeys.all, 'detail'] as const,
  detail: (id: string) => [...bookingKeys.details(), id] as const,
  stats: (params: any) => [...bookingKeys.all, 'stats', params] as const,
  today: () => [...bookingKeys.all, 'today'] as const,
  user: (userId: string, params: any) => [...bookingKeys.all, 'user', userId, params] as const,
};

// Hooks
export function useBookings(filters: BookingFilters = {}) {
  return useQuery({
    queryKey: bookingKeys.list(filters),
    queryFn: async () => {
      const response = await server.api.bookings.get({
        query: filters
      });

      if (response.error) {
        throw new Error('Failed to fetch bookings');
      }

      return response.data;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useBooking(id: string) {
  return useQuery({
    queryKey: bookingKeys.detail(id),
    queryFn: async () => {
      const response = await server.api.bookings({ id }).get();

      if (response.error) {
        throw new Error('Failed to fetch booking');
      }

      return response.data;
    },
    enabled: !!id,
    staleTime: 2 * 60 * 1000,
  });
}

export function useBookingStats(params: any = {}) {
  return useQuery({
    queryKey: bookingKeys.stats(params),
    queryFn: async () => {
      const response = await server.api.bookings.stats.get({
        query: params
      });

      if (response.error) {
        throw new Error('Failed to fetch booking stats');
      }

      return response.data;
    },
    staleTime: 5 * 60 * 1000,
  });
}

export function useTodayBookings() {
  return useQuery({
    queryKey: bookingKeys.today(),
    queryFn: async () => {
      const response = await server.api.bookings.today.get();

      if (response.error) {
        throw new Error('Failed to fetch today bookings');
      }

      return response.data;
    },
    staleTime: 1 * 60 * 1000,
    refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes
  });
}

export function useCreateBooking() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateBookingInput) => {
      const response = await server.api.bookings.post(data);
      console.log('response', response);
      if (response.error) {
        throw new Error('Failed to create booking');
      }

      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: bookingKeys.lists() });
      queryClient.invalidateQueries({ queryKey: bookingKeys.today() });
      queryClient.invalidateQueries({ queryKey: bookingKeys.stats({}) });
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useUpdateBooking() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<CreateBookingInput> }) => {
      const response = await server.api.bookings({ id }).put(data);

      if (response.error) {
        throw new Error('Failed to update booking');
      }

      return response.data;
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: bookingKeys.lists() });
      queryClient.invalidateQueries({ queryKey: bookingKeys.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: bookingKeys.today() });
      toast.success('Booking updated successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useCancelBooking() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, reason }: { id: string; reason?: string }) => {
      const response = await server.api.bookings({ id }).cancel.post({
        reason
      });

      if (response.error) {
        throw new Error('Failed to cancel booking');
      }

      return response.data;
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: bookingKeys.lists() });
      queryClient.invalidateQueries({ queryKey: bookingKeys.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: bookingKeys.today() });
      toast.success('Booking cancelled successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useCheckInBooking() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await server.api.bookings({ id })['check-in'].post({});

      if (response.error) {
        throw new Error('Failed to check in');
      }

      return response.data;
    },
    onSuccess: (data, id) => {
      queryClient.invalidateQueries({ queryKey: bookingKeys.lists() });
      queryClient.invalidateQueries({ queryKey: bookingKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: bookingKeys.today() });
      toast.success('Checked in successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useCheckOutBooking() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await server.api.bookings({ id })['check-out'].post({});

      if (response.error) {
        throw new Error('Failed to check out');
      }

      return response.data;
    },
    onSuccess: (data, id) => {
      queryClient.invalidateQueries({ queryKey: bookingKeys.lists() });
      queryClient.invalidateQueries({ queryKey: bookingKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: bookingKeys.today() });
      toast.success('Checked out successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}
