### Test Booking Creation Fix
### This file tests the booking creation with resourceId field

### 1. First, get available meeting rooms
GET http://localhost:4002/api/meeting-rooms
Content-Type: application/json

### 2. Create a booking with resourceId (replace ROOM_ID with actual ID from step 1)
POST http://localhost:4002/api/bookings
Content-Type: application/json

{
  "resourceType": "MEETING_ROOM",
  "resourceId": "cmcmi9gd10002v65ofhskh4t6",
  "title": "Test Booking - Fixed ResourceId",
  "description": "Testing that resourceId field is now included",
  "startDate": "2025-07-04T10:00:00.000Z",
  "endDate": "2025-07-04T11:00:00.000Z",
  "attendees": {
    "internal": ["<EMAIL>"],
    "external": ["<EMAIL>"]
  }
}

### 3. Get all bookings to verify creation
GET http://localhost:4002/api/bookings
Content-Type: application/json
