'use client';

import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, Users, MapPin, User, Mail, Phone, Building } from 'lucide-react';
import { DateTime } from 'luxon';
import { EventModalProps } from './types';

export function EventModal({ isOpen, onClose, booking, onCancel, canCancel }: EventModalProps) {
  if (!booking) return null;

  const startDate = DateTime.fromJSDate(new Date(booking.startDate));
  const endDate = DateTime.fromJSDate(new Date(booking.endDate));

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'CONFIRMED':
        return 'bg-blue-100 text-blue-800';
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      case 'NO_SHOW':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'CONFIRMED':
        return 'Confirmada';
      case 'COMPLETED':
        return 'Completada';
      case 'CANCELLED':
        return 'Cancelada';
      case 'NO_SHOW':
        return 'No se presentó';
      default:
        return status;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-purple-600" />
            {booking.title}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Status */}
          <div className="flex items-center justify-between">
            <Badge className={getStatusColor(booking.status)}>
              {getStatusText(booking.status)}
            </Badge>
          </div>

          {/* Date and Time */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600">Fecha:</span>
              <span className="font-medium">
                {startDate.toFormat('dd/MM/yyyy')}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600">Horario:</span>
              <span className="font-medium">
                {startDate.toFormat('HH:mm')} - {endDate.toFormat('HH:mm')}
              </span>
            </div>
          </div>

          {/* Room */}
          {booking.meetingRoom && (
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600">Sala:</span>
              <span className="font-medium">{booking.meetingRoom.name}</span>
            </div>
          )}

          {/* Description */}
          {booking.description && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Descripción</h4>
              <p className="text-gray-600 text-sm">{booking.description}</p>
            </div>
          )}

          {/* Organizer */}
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Organizador</h4>
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-gray-500" />
              <span className="text-sm">{booking.user?.name}</span>
              {booking.user?.email && (
                <>
                  <Mail className="h-4 w-4 text-gray-500 ml-2" />
                  <span className="text-sm text-gray-600">{booking.user.email}</span>
                </>
              )}
            </div>
          </div>

          {/* Guests */}
          {booking.guests && booking.guests.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                <Users className="h-4 w-4" />
                Invitados ({booking.guests.length})
              </h4>
              <div className="space-y-2">
                {booking.guests.map((guest: any, index: number) => (
                  <div key={index} className="flex items-center gap-2 text-sm">
                    <User className="h-3 w-3 text-gray-400" />
                    <span>{guest.name}</span>
                    {guest.email && (
                      <>
                        <Mail className="h-3 w-3 text-gray-400 ml-2" />
                        <span className="text-gray-600">{guest.email}</span>
                      </>
                    )}
                    {guest.phone && (
                      <>
                        <Phone className="h-3 w-3 text-gray-400 ml-2" />
                        <span className="text-gray-600">{guest.phone}</span>
                      </>
                    )}
                    {guest.company && (
                      <>
                        <Building className="h-3 w-3 text-gray-400 ml-2" />
                        <span className="text-gray-600">{guest.company}</span>
                      </>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Cerrar
            </Button>
            {canCancel && (
              <Button 
                variant="destructive" 
                onClick={() => onCancel(booking)}
              >
                Cancelar Reserva
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
