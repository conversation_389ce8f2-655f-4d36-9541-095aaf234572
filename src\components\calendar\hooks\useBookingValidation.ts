import { useCallback } from 'react';
import { DateTime } from 'luxon';
import { ValidationResult } from '../types';

interface UseBookingValidationProps {
  selectedRoom?: any;
  internalAttendees?: string[];
  externalAttendees?: string[];
}

export function useBookingValidation({
  selectedRoom,
  internalAttendees = [],
  externalAttendees = []
}: UseBookingValidationProps = {}) {

  const validateBookingRules = useCallback((
    startTime: string,
    endTime: string,
    selectedDate: Date
  ): ValidationResult => {
    if (!selectedRoom?.bookingRules) {
      return { isValid: true, error: '' };
    }

    const rules = selectedRoom.bookingRules;
    
    // Parse times
    const [startHour, startMinute] = startTime.split(':').map(Number);
    const [endHour, endMinute] = endTime.split(':').map(Number);
    
    const start = DateTime.fromJSDate(selectedDate).set({ 
      hour: startHour, 
      minute: startMinute 
    });
    const end = DateTime.fromJSDate(selectedDate).set({ 
      hour: endHour, 
      minute: endMinute 
    });

    // Validate duration
    const durationMinutes = end.diff(start, 'minutes').minutes;
    
    if (rules.minBookingDuration && durationMinutes < rules.minBookingDuration) {
      return {
        isValid: false,
        error: `La duración mínima de reserva es ${rules.minBookingDuration} minutos`
      };
    }

    if (rules.maxBookingDuration && durationMinutes > rules.maxBookingDuration) {
      return {
        isValid: false,
        error: `La duración máxima de reserva es ${rules.maxBookingDuration} minutos`
      };
    }

    // Validate business hours
    if (rules.businessHours) {
      const businessStart = DateTime.fromFormat(rules.businessHours.start || '09:00', 'HH:mm');
      const businessEnd = DateTime.fromFormat(rules.businessHours.end || '18:00', 'HH:mm');

      if (start < businessStart || end > businessEnd) {
        return {
          isValid: false,
          error: `Las reservas deben estar dentro del horario laboral (${rules.businessHours.start || '09:00'} - ${rules.businessHours.end || '18:00'})`
        };
      }
    }

    return { isValid: true, error: '' };
  }, [selectedRoom]);

  const validateCapacity = useCallback((): ValidationResult => {
    if (!selectedRoom?.capacity) {
      return { isValid: true, error: '' };
    }

    const totalAttendees = internalAttendees.filter(email => email.trim() !== '').length + 
                          externalAttendees.filter(email => email.trim() !== '').length + 
                          1; // +1 for the organizer

    if (totalAttendees > selectedRoom.capacity) {
      return {
        isValid: false,
        error: `La sala tiene capacidad para ${selectedRoom.capacity} personas. Tienes ${totalAttendees} asistentes.`
      };
    }

    return { isValid: true, error: '' };
  }, [selectedRoom, internalAttendees, externalAttendees]);

  const validateTimeRange = useCallback((startTime: string, endTime: string): ValidationResult => {
    if (!startTime || !endTime) {
      return { isValid: false, error: 'Hora de inicio y fin son requeridas' };
    }

    const [startHour, startMinute] = startTime.split(':').map(Number);
    const [endHour, endMinute] = endTime.split(':').map(Number);
    
    const startMinutes = startHour * 60 + startMinute;
    const endMinutes = endHour * 60 + endMinute;

    if (endMinutes <= startMinutes) {
      return { isValid: false, error: 'La hora de fin debe ser posterior a la hora de inicio' };
    }

    return { isValid: true, error: '' };
  }, []);

  const validatePastDate = useCallback((selectedDate: Date): ValidationResult => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const bookingDate = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate());

    if (bookingDate < today) {
      return { isValid: false, error: 'No se pueden crear reservas en fechas pasadas' };
    }

    return { isValid: true, error: '' };
  }, []);

  return {
    validateBookingRules,
    validateCapacity,
    validateTimeRange,
    validatePastDate
  };
}
