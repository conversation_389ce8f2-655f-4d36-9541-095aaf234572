'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Calendar, Clock } from 'lucide-react';
import { DateTime } from 'luxon';
import { CancelBookingDialogProps } from './types';

export function CancelBookingDialog({ 
  isOpen, 
  onClose, 
  onConfirm, 
  booking,
  isLoading = false 
}: CancelBookingDialogProps) {
  if (!booking) return null;

  const startDate = DateTime.fromJSDate(new Date(booking.startDate));
  const endDate = DateTime.fromJSDate(new Date(booking.endDate));

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>¿Cancelar reserva?</AlertDialogTitle>
          <AlertDialogDescription asChild>
            <div className="space-y-3">
              <p>
                ¿Estás seguro de que quieres cancelar esta reserva? Esta acción no se puede deshacer.
              </p>
              
              <div className="bg-gray-50 p-3 rounded-lg space-y-2">
                <h4 className="font-medium text-gray-900">{booking.title}</h4>
                
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    {startDate.toFormat('dd/MM/yyyy')}
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    {startDate.toFormat('HH:mm')} - {endDate.toFormat('HH:mm')}
                  </div>
                </div>
                
                {booking.meetingRoom && (
                  <p className="text-sm text-gray-600">
                    Sala: {booking.meetingRoom.name}
                  </p>
                )}
              </div>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>
            No, mantener reserva
          </AlertDialogCancel>
          <AlertDialogAction 
            onClick={onConfirm}
            disabled={isLoading}
            className="bg-red-600 hover:bg-red-700"
          >
            {isLoading ? 'Cancelando...' : 'Sí, cancelar reserva'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
