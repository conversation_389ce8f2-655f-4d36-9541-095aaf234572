// Test script to verify booking creation fix
const testBookingCreation = async () => {
  const bookingData = {
    resourceType: "MEETING_ROOM",
    resourceId: "cmcmi9gd10002v65ofhskh4t6", // Use existing room ID
    title: "Test Booking - Fixed ResourceId",
    description: "Testing that resourceId field is now included",
    startDate: "2025-07-04T10:00:00.000Z",
    endDate: "2025-07-04T11:00:00.000Z",
    attendees: {
      internal: ["<EMAIL>"],
      external: ["<EMAIL>"]
    }
  };

  try {
    console.log('Testing booking creation with data:', JSON.stringify(bookingData, null, 2));

    const response = await fetch('http://localhost:4002/api/bookings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // 'x-user-id': 'temp-user-id' // Add user ID header for auth
        'Cookie': 'better-auth.session_token=KD8i6hPSS5TIBqHunp5szwPDkjmvT515.PDF0YAUs%2FqkiXYIiczZEdePFyLHCacL6i3eL201wtig%3D'
      },
      body: JSON.stringify(bookingData)
    });

    const result = await response.json();

    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(result, null, 2));

    if (result.success) {
      console.log('✅ SUCCESS: Booking created successfully!');
      console.log('Booking ID:', result.data.id);
    } else {
      console.log('❌ ERROR:', result.error);
    }
  } catch (error) {
    console.error('❌ NETWORK ERROR:', error.message);
  }
};

// Run the test
testBookingCreation();
