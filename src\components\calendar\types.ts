
export interface BookingEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  resource?: any;
  booking: any;
}

export interface CreateBookingFormData {
  title: string;
  description?: string;
  resourceId: string;
  startTime: string;
  endTime: string;
  internalAttendees?: string[];
  externalAttendees?: string[];
}

export interface SelectedSlot {
  start: Date;
  end: Date;
  resourceId?: string;
}

export interface EventModalProps {
  isOpen: boolean;
  onClose: () => void;
  booking: any;
  onCancel: (booking: any) => void;
  canCancel: boolean;
}

export interface CreateBookingModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedSlot: SelectedSlot | null;
  meetingRooms: any[];
  onSubmit: (data: CreateBookingFormData) => Promise<void>;
  isLoading?: boolean;
}

export interface CancelBookingDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  booking: any;
  isLoading?: boolean;
}

export interface CalendarToolbarProps {
  onNavigate: (action: 'PREV' | 'NEXT' | 'TODAY') => void;
  onView: (view: string) => void;
  currentView: string;
  currentDate: Date;
}

export interface ValidationResult {
  isValid: boolean;
  error: string;
}

export interface BookingRules {
  minBookingDuration?: number;
  maxBookingDuration?: number;
  businessHours?: {
    start: string;
    end: string;
  };
  capacity?: number;
}
